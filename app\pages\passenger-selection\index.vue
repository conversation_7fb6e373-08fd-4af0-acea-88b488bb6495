<script lang="ts" setup>
import { ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useRouteStore } from '~/stores/bus-service-selection'
import PassengerDatePicker from './components/PassengerDatePicker.vue'
import StopSelectionDialog from './components/StopSelectionDialog.vue'
import PassengerSelectionDialog from './components/PassengerSelectionDialog.vue'
import MapSelectionDialog from './components/MapSelectionDialog.vue'
import SeatSelectionDialog from './components/SeatSelectionDialog.vue'
import LoginDialog from './components/LoginDialog.vue'
definePageMeta({
  footerPlaceholderHeight: 100
})
const router = useRouter()
const routeStore = useRouteStore()

/* ダイアログ状態管理 */
const datePickerOpen = ref(false)
const stopSelectionOpen = ref(false)
const dropOffSelectionOpen = ref(false)
const passengerSelectionOpen = ref(false)
const mapSelectionOpen = ref(false)
const seatSelectionOpen = ref(false)
const loginDialogOpen = ref(false)
const currentTripIndex = ref<number | null>(null)
const currentRouteId = ref<number | null>(null)
const currentServiceIndex = ref<number | null>(null)
const selectionType = ref<'pickup' | 'dropoff'>('pickup')

/* ユーザー認証状態管理 */
const isLoggedIn = ref(false) // 実際のプロジェクトでは認証ストアから取得

const expandedAmenities = ref<{ [key: number]: boolean }>({})

/* 現在の日付を取得 */
const getCurrentDate = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  const day = now.getDate()
  return `${year}-${month}-${day}`
}
const selectedDate = ref(getCurrentDate())

const busSchedules = ref([
  { number: 1, availableSeats: 10 },
  { number: 2, availableSeats: 13 },
  { number: 3, availableSeats: 4 }
])

const selectedTrip = ref<number | null>(null)

/* Piniaストアからバスサービス一覧を取得 */
const tripSchedules = computed(() => {
  // 强制响应式更新，确保能检测到 Pinia store 的变化
  return routeStore.getCurrentBusServices
})

/* 計算プロパティ */
const selectedYear = computed(() => {
  if (!selectedDate.value) {
    return new Date().getFullYear().toString()
  }
  return selectedDate.value.split('-')[0]
})

/* 確認ボタンの有効状態を判定 */
const isConfirmButtonEnabled = computed(() => {
  if (selectedTrip.value === null) return false

  const trip = tripSchedules.value[selectedTrip.value]
  if (!trip) return false

  // 乗り場選択、降り場選択、人数選択がすべて入力されているかチェック
  const hasPickupStop = trip.selectedStop && trip.selectedStop.trim() !== ''
  const hasDropoffStop =
    trip.selectedDropOff && trip.selectedDropOff.trim() !== ''
  const hasPassengerInfo =
    (trip.adultCount && trip.adultCount > 0) ||
    (trip.passengerDetails && trip.passengerDetails.trim() !== '')

  return hasPickupStop && hasDropoffStop && hasPassengerInfo
})

const selectedDateDisplay = computed(() => {
  if (!selectedDate.value) {
    const now = new Date()
    const month = now.getMonth() + 1
    const day = now.getDate()
    const weekdays = ['日', '月', '火', '水', '木', '金', '土']
    const weekday = weekdays[now.getDay()]
    return `${month}/${day}(${weekday})`
  }

  const parts = selectedDate.value.split('-')
  if (parts.length !== 3) {
    const now = new Date()
    const month = now.getMonth() + 1
    const day = now.getDate()
    const weekdays = ['日', '月', '火', '水', '木', '金', '土']
    const weekday = weekdays[now.getDay()]
    return `${month}/${day}(${weekday})`
  }

  const [year, month, day] = parts
  if (!year || !month || !day) return '3/24(月)'

  const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
  const weekdays = ['日', '月', '火', '水', '木', '金', '土']
  const weekday = weekdays[date.getDay()]

  return `${parseInt(month)}/${parseInt(day)}(${weekday})`
})

const goBack = () => {
  router.back()
}

/* 障害者人数を計算 */
const calculateDisabilityCount = (passengerDetails: string): number => {
  if (!passengerDetails) return 0

  const disabilityAdultMatch = passengerDetails.match(
    /障害者大人：[^/]*男性(\d+)名|障害者大人：[^/]*女性(\d+)名/g
  )
  const disabilityChildMatch = passengerDetails.match(
    /障害者子供：[^/]*男性(\d+)名|障害者子供：[^/]*女性(\d+)名/g
  )

  let totalDisability = 0

  if (disabilityAdultMatch) {
    disabilityAdultMatch.forEach((match) => {
      const numbers = match.match(/(\d+)名/g)
      if (numbers) {
        numbers.forEach((num) => {
          totalDisability += parseInt(num.replace('名', ''))
        })
      }
    })
  }

  if (disabilityChildMatch) {
    disabilityChildMatch.forEach((match) => {
      const numbers = match.match(/(\d+)名/g)
      if (numbers) {
        numbers.forEach((num) => {
          totalDisability += parseInt(num.replace('名', ''))
        })
      }
    })
  }

  return totalDisability
}

/* テキストオーバーフロー判定 */
const isTextOverflowing = (
  text: string | null | number,
  maxLength: number = 20
): boolean => {
  if (!text) return false
  return String(text).length > maxLength
}
const isTextOverflowing2 = (
  text: string | null | number,
  maxLength: number = 13
): boolean => {
  if (!text) return false
  return String(text).length > maxLength
}

const openDatePicker = () => {
  datePickerOpen.value = true
}

const closeDatePicker = () => {
  datePickerOpen.value = false
}

const handleDateSelected = (dateString: string) => {
  selectedDate.value = dateString
  closeDatePicker()
}

const openFilter = () => {
  /* 絞り込み機能の実装 */
}

const refreshData = () => {
  /* データ更新機能の実装 */
}

/* 設備情報の展開/収起を切り替え */
const toggleAmenities = (tripIndex: number) => {
  expandedAmenities.value[tripIndex] = !expandedAmenities.value[tripIndex]
}

/* 表示する設備情報を取得 */
const getDisplayedAmenities = (amenities: any[], tripIndex: number) => {
  const isExpanded = expandedAmenities.value[tripIndex] || false
  if (isExpanded || amenities.length <= 6) {
    return amenities
  }
  return amenities.slice(0, 6)
}

const selectTrip = (index: number) => {
  selectedTrip.value = index
}

const proceedToNextStep = () => {
  // ログイン状態をチェック
  if (!isLoggedIn.value) {
    loginDialogOpen.value = true
    return
  }

  if (selectedTrip.value !== null) {
    router.push('/appointment/confirmation')
  }
}

/* 停留所選択関連 */
const openStopSelection = (tripIndex: number) => {
  const routeId = Math.floor(tripIndex / 1) + 1 // routeIdを計算
  currentTripIndex.value = tripIndex
  currentRouteId.value = routeId
  currentServiceIndex.value = 0 // 各ルートの最初のサービス
  selectionType.value = 'pickup'
  stopSelectionOpen.value = true
}

const openDropOffSelection = (tripIndex: number) => {
  const routeId = Math.floor(tripIndex / 1) + 1 // routeIdを計算
  currentTripIndex.value = tripIndex
  currentRouteId.value = routeId
  currentServiceIndex.value = 0 // 各ルートの最初のサービス
  selectionType.value = 'dropoff'
  dropOffSelectionOpen.value = true
}

const handleStopSelected = (stop: {
  time: string
  name: string
  id?: string
}) => {
  if (currentRouteId.value !== null && currentServiceIndex.value !== null) {
    routeStore.updateStopSelection(
      currentRouteId.value,
      currentServiceIndex.value,
      'pickup',
      stop.name
    )
  }
  stopSelectionOpen.value = false
  currentTripIndex.value = null
  currentRouteId.value = null
  currentServiceIndex.value = null
}

const handleDropOffSelected = (stop: {
  time: string
  name: string
  id?: string
}) => {
  if (currentRouteId.value !== null && currentServiceIndex.value !== null) {
    routeStore.updateStopSelection(
      currentRouteId.value,
      currentServiceIndex.value,
      'dropoff',
      stop.name
    )
  }
  dropOffSelectionOpen.value = false
  currentTripIndex.value = null
  currentRouteId.value = null
  currentServiceIndex.value = null
}

const handleMapSelect = () => {
  stopSelectionOpen.value = false
  dropOffSelectionOpen.value = false
  mapSelectionOpen.value = true
}

/* 地図選択からの停留所選択処理 */
const handleMapStopSelected = (data: any) => {
  if (currentRouteId.value !== null && currentServiceIndex.value !== null) {
    routeStore.updateStopSelection(
      currentRouteId.value,
      currentServiceIndex.value,
      selectionType.value,
      data.name
    )
  }
  mapSelectionOpen.value = false
  currentTripIndex.value = null
  currentRouteId.value = null
  currentServiceIndex.value = null
}

/* 乗客選択関連 */
const openPassengerSelection = (tripIndex: number) => {
  const routeId = Math.floor(tripIndex / 1) + 1
  currentTripIndex.value = tripIndex
  currentRouteId.value = routeId
  currentServiceIndex.value = 0
  passengerSelectionOpen.value = true
}

const handlePassengerSelected = (counts: any) => {
  if (currentRouteId.value !== null && currentServiceIndex.value !== null) {
    passengerCountsData.value[currentTripIndex.value!] = counts

    const totalAdults = counts.adult.male + counts.adult.female
    const totalWheelchair =
      counts.disabilityAdult.male +
      counts.disabilityAdult.female +
      counts.disabilityChild.male +
      counts.disabilityChild.female

    routeStore.updatePassengerInfo(
      currentRouteId.value,
      currentServiceIndex.value,
      {
        adultCount: totalAdults,
        wheelchairCount: totalWheelchair.toString(),
        passengerDetails: null
      }
    )
  }
  passengerSelectionOpen.value = false
  currentTripIndex.value = null
  currentRouteId.value = null
  currentServiceIndex.value = null
}

const handleReserveWithoutSeat = (data: any) => {
  if (currentRouteId.value !== null && currentServiceIndex.value !== null) {
    passengerCountsData.value[currentTripIndex.value!] = data.counts

    const counts = data.counts
    const totalAdults = counts.adult.male + counts.adult.female

    routeStore.updatePassengerInfo(
      currentRouteId.value,
      currentServiceIndex.value,
      {
        adultCount: totalAdults,
        passengerDetails: data.formattedInfo,
        wheelchairCount: data.wheelchairInfo || data.formattedInfo // wheelchairCountも同期
      }
    )

    // 强制触发响应式更新
    nextTick(() => {
      forceRefresh()
    })
  }
  passengerSelectionOpen.value = false
  currentTripIndex.value = null
  currentRouteId.value = null
  currentServiceIndex.value = null
}

const handleProceedToSeat = (counts: any) => {
  handlePassengerSelected(counts)
}

/* 座席選択関連 */
const openSeatSelection = (tripIndex: number) => {
  const routeId = Math.floor(tripIndex / 1) + 1
  currentTripIndex.value = tripIndex
  currentRouteId.value = routeId
  currentServiceIndex.value = 0
  seatSelectionOpen.value = true
}

const passengerCountsData = ref<{ [key: number]: any }>({})

// 响应式刷新键，用于强制更新组件
const refreshKey = ref(0)
const forceRefresh = () => {
  refreshKey.value++
}

/* 返回顶部按钮 */
const showBackToTop = ref(false)

const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

const getCurrentPassengerCounts = () => {
  if (currentTripIndex.value !== null) {
    if (passengerCountsData.value[currentTripIndex.value]) {
      return passengerCountsData.value[currentTripIndex.value]
    }

    const trip = tripSchedules.value[currentTripIndex.value]
    if (trip) {
      const adultCount = trip.adultCount || 0
      return {
        adult: {
          male: Math.floor(adultCount / 2),
          female: Math.ceil(adultCount / 2)
        },
        child: { male: 0, female: 0 },
        student: { male: 0, female: 0 },
        disabilityAdult: { male: 0, female: 0 },
        disabilityChild: { male: 0, female: 0 },
        disability: { male: 0, female: 0 }
      }
    }
  }
  return {
    adult: { male: 1, female: 1 },
    child: { male: 1, female: 1 },
    student: { male: 0, female: 0 },
    disabilityAdult: { male: 0, female: 0 },
    disabilityChild: { male: 0, female: 0 },
    disability: { male: 0, female: 0 }
  }
}

// デフォルトの乗客表示情報を生成する関数
const getDefaultPassengerDisplay = (trip: any) => {
  if (!trip.adultCount || trip.adultCount === 0) {
    return '人数を選択してください'
  }

  // adultCountから基本的な表示を生成
  const male = Math.floor(trip.adultCount / 2)
  const female = Math.ceil(trip.adultCount / 2)

  const details: string[] = []
  if (male > 0) details.push(`男性${male}名`)
  if (female > 0) details.push(`女性${female}名`)

  return `大人：${details.join('、')}`
}

// デフォルトの座席表示情報を生成する関数
const getDefaultWheelchairDisplay = (trip: any) => {
  if (!trip.adultCount || trip.adultCount === 0) {
    return '座席を選択してください'
  }

  // 選択された座席がある場合は座席番号を表示
  if (trip.selectedSeats && trip.selectedSeats.length > 0) {
    return trip.selectedSeats.join('、')
  }

  // adultCountから基本的な座席表示を生成
  const male = Math.floor(trip.adultCount / 2)
  const female = Math.ceil(trip.adultCount / 2)

  const details: string[] = []
  if (male > 0) details.push(`男性: ${male}名`)
  if (female > 0) details.push(`女性: ${female}名`)

  return `大人 - ${details.join(', ')}`
}

const handleSeatsSelected = (seats: string[]) => {
  if (currentRouteId.value !== null && currentServiceIndex.value !== null) {
    routeStore.updateSeatSelection(
      currentRouteId.value,
      currentServiceIndex.value,
      seats
    )
  }
  seatSelectionOpen.value = false
  currentTripIndex.value = null
  currentRouteId.value = null
  currentServiceIndex.value = null
}

/* ログイン関連の処理 */
const handleLogin = () => {
  // ログインページに遷移またはログイン処理を実行
  console.log('ログインページに遷移')
  // 実際の実装では以下のような処理を行う
  // router.push('/login')
  // または認証APIを呼び出し
}

const handleRegister = () => {
  // 新規会員登録ページに遷移
  console.log('新規会員登録ページに遷移')
  // 実際の実装では以下のような処理を行う
  // router.push('/register')
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})

useHead({
  title: '停留所・乗車人数選択'
})
</script>

<template>
  <div class="passenger-selection-page">
    <div class="page-header">
      <div class="header-content">
        <v-btn icon variant="text" @click="goBack" class="back-button">
          <v-icon color="#26499D" size="24">mdi-arrow-left</v-icon>
        </v-btn>

        <div class="header-info">
          <div class="page-title">停留所・乗車人数選択</div>
        </div>
      </div>
    </div>
    <!-- ルート方向表示 -->
    <div class="route-direction-indicator">
      <div
        v-if="routeStore.currentSelectedRouteDirection === 'outbound'"
        class="direction-text"
      >
        往路选择中
      </div>
      <div
        v-else-if="routeStore.currentSelectedRouteDirection === 'inbound'"
        class="direction-text"
      >
        復路选择中
      </div>
    </div>
    <div class="main-content">
      <div class="guidance-section">
        <div class="guidance-content">
          <p class="guidance-text">空席状况</p>
          <v-btn
            variant="outlined"
            size="small"
            class="refresh-button"
            @click="refreshData"
          >
            <v-icon left color="#26499D" size="16">mdi-refresh</v-icon>
            更新
          </v-btn>
        </div>
      </div>

      <div class="bus-schedule-list">
        <div
          v-for="(schedule, index) in busSchedules"
          :key="index"
          class="schedule-item"
        >
          <div class="schedule-number">{{ schedule.number }}便目</div>
          <div class="seats-info">
            <span class="seats-count">{{ schedule.availableSeats }}席</span>
            <span v-if="schedule.availableSeats <= 4" class="low-seats-warning">
              残り僅か
            </span>
          </div>
        </div>
      </div>
      <!-- アクションボタンコンテナ -->
      <div class="action-buttons-container">
        <!-- 一時保存ボタン（無効状態） -->
        <v-btn
          variant="flat"
          class="temp-save-btn"
          disabled
          prepend-icon="mdi-content-save"
        >
          一時保存
        </v-btn>
      </div>
      <div class="date-selection-area">
        <div class="date-container">
          <div class="date-info-section">
            <div class="date-label">出発日</div>
            <div class="date-display-box">
              <div class="year-text">{{ selectedYear }}年</div>
              <div class="date-text">{{ selectedDateDisplay }}</div>
            </div>
          </div>
        </div>
        <div class="btn-center-area">
          <v-btn
            variant="outlined"
            class="date-change-button"
            @click="openDatePicker"
          >
            <v-icon left size="20" color="#26499D">mdi-calendar</v-icon>
            日付変更
          </v-btn>
        </div>

        <div class="filter-button-area">
          <v-btn variant="outlined" class="filter-button" @click="openFilter">
            <v-icon left size="20" color="#26499D">mdi-filter-variant</v-icon>
            絞り込み
          </v-btn>
        </div>
      </div>

      <div class="bus-content-area">
        <div class="content-placeholder">
          <div class="bus-schedule-container">
            <div
              v-for="(trip, index) in tripSchedules"
              :key="`${trip.tripNumber}-${index}-${refreshKey}`"
              class="trip-card"
              @click="selectTrip(index)"
            >
              <div class="trip-header">
                <div class="trip-left-section">
                  <div class="trip-number">{{ trip.tripNumber }}</div>
                  <div class="route-info">{{ trip.route }}</div>
                </div>
                <div class="trip-right-section">
                  <span class="rating-count">(000)</span>
                </div>
              </div>
              <div class="trip-card-content">
                <div class="section-title">
                  <div class="bus-info-section">
                    <div class="tate-info-flex">
                      <div class="date-info">{{ trip.date }}</div>
                      <div class="date-info">{{ trip.datePicker }}</div>
                    </div>
                    <div class="service-info">
                      {{ trip.serviceName }} {{ trip.route
                      }}{{ trip.departureTime }}発
                    </div>
                    <div class="company-info">
                      バス会社：{{ trip.operator }}
                    </div>
                  </div>
                </div>
                <div class="time-status-container">
                  <div class="time-display">
                    <span class="time-text">000時間00分</span>
                  </div>

                  <div class="status-icons">
                    <v-btn
                      class="status-btn safety-btn"
                      icon
                      size="large"
                      color="#FF6B5A"
                    >
                      <span class="status-text">安</span>
                    </v-btn>

                    <v-btn
                      class="status-btn early-btn"
                      icon
                      size="large"
                      color="#5DADE2"
                    >
                      <span class="status-text">早</span>
                    </v-btn>

                    <v-btn
                      class="status-btn comfort-btn"
                      icon
                      size="large"
                      color="#58D68D"
                    >
                      <span class="status-text">楽</span>
                    </v-btn>
                  </div>
                </div>
                <div class="section-content">
                  <div class="amenities">
                    <div class="amenities-tags">
                      <v-chip
                        v-for="(amenity, amenityIndex) in getDisplayedAmenities(
                          trip.amenities,
                          index
                        )"
                        :key="amenity.name"
                        variant="outlined"
                        size="small"
                        class="amenity-chip"
                      >
                        <v-icon
                          v-if="amenity.icon"
                          :color="amenity.iconColor || '#F2B774'"
                          size="16"
                          start
                        >
                          {{ amenity.icon }}
                        </v-icon>
                        {{ amenity.name }}
                      </v-chip>
                    </div>
                    <div
                      v-if="trip.amenities.length > 5"
                      class="toggle-button"
                      @click="toggleAmenities(index)"
                    >
                      {{ expandedAmenities[index] ? '閉じる' : '…すべて見る' }}
                    </div>
                  </div>
                </div>

                <div class="tags-section"></div>

                <div class="selection-fields">
                  <div class="input-field">
                    <label :for="`pickup-stop-${index}`" class="input-label">
                      乗り場選択
                    </label>
                    <v-text-field
                      :id="`pickup-stop-${index}`"
                      v-model="trip.selectedStop"
                      placeholder="停留所を入力してください"
                      variant="outlined"
                      density="compact"
                      class="stop-input"
                      hide-details
                      readonly
                      append-inner-icon="mdi-chevron-right"
                      @click="openStopSelection(index)"
                    ></v-text-field>
                  </div>

                  <div class="input-field">
                    <label :for="`dropoff-stop-${index}`" class="input-label">
                      降り場選択
                    </label>
                    <v-text-field
                      :id="`dropoff-stop-${index}`"
                      v-model="trip.selectedDropOff"
                      placeholder="停留所を入力してください"
                      variant="outlined"
                      density="compact"
                      class="passenger-input"
                      hide-details
                      readonly
                      append-inner-icon="mdi-chevron-right"
                      @click="openDropOffSelection(index)"
                    ></v-text-field>
                  </div>
                </div>

                <div class="selection-fields-tag">
                  <div class="input-label">人数・座席選択</div>
                  <div
                    v-if="
                      isTextOverflowing(trip.passengerDetails) ||
                      isTextOverflowing2(trip.wheelchairCount)
                    "
                    class="passenger-details-stacked"
                  >
                    <div class="passenger-field-full">
                      <v-textarea
                        :model-value="
                          trip.passengerDetails ||
                          getDefaultPassengerDisplay(trip)
                        "
                        variant="outlined"
                        density="compact"
                        class="passenger-details-textarea"
                        hide-details
                        readonly
                        prepend-inner-icon="mdi-human-male"
                        background-color="white"
                        rows="2"
                        auto-grow
                        no-resize
                        @click="openPassengerSelection(index)"
                      ></v-textarea>
                    </div>

                    <div class="passenger-field-full">
                      <v-textarea
                        :model-value="
                          trip.wheelchairCount ||
                          getDefaultWheelchairDisplay(trip)
                        "
                        variant="outlined"
                        density="compact"
                        class="passenger-display-input"
                        hide-details
                        readonly
                        prepend-inner-icon="mdi-seat-passenger"
                        background-color="white"
                        rows="2"
                        auto-grow
                        no-resize
                        @click="openSeatSelection(index)"
                      ></v-textarea>
                    </div>
                  </div>

                  <div v-else class="passenger-count-fields">
                    <div class="passenger-field">
                      <v-text-field
                        :model-value="
                          trip.passengerDetails ||
                          getDefaultPassengerDisplay(trip)
                        "
                        variant="outlined"
                        density="compact"
                        class="passenger-display-input"
                        hide-details
                        readonly
                        prepend-inner-icon="mdi-human-male"
                        background-color="white"
                        @click="openPassengerSelection(index)"
                      ></v-text-field>
                    </div>

                    <div class="passenger-field">
                      <v-text-field
                        :model-value="
                          trip.wheelchairCount ||
                          getDefaultWheelchairDisplay(trip)
                        "
                        variant="outlined"
                        density="compact"
                        class="passenger-display-input"
                        hide-details
                        readonly
                        prepend-inner-icon="mdi-seat-passenger"
                        background-color="white"
                        @click="openSeatSelection(index)"
                      ></v-text-field>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="footer-actions">
            <v-btn
              class="primary-action-button"
              :disabled="selectedTrip === null"
              @click="proceedToNextStep"
              rounded="8"
              block
            >
              この便を変更
            </v-btn>
          </div>
        </div>
      </div>
    </div>

    <v-overlay
      v-model="datePickerOpen"
      class="align-center justify-center"
      @click:outside="closeDatePicker"
    >
      <PassengerDatePicker
        :selectedDateValue="selectedDate"
        @date-selected="handleDateSelected"
        @close="closeDatePicker"
      />
    </v-overlay>

    <StopSelectionDialog
      v-model="stopSelectionOpen"
      title="乗り場一覧"
      @stop-selected="handleStopSelected"
      @map-select="handleMapSelect"
    />

    <StopSelectionDialog
      v-model="dropOffSelectionOpen"
      title="降り場一覧"
      @stop-selected="handleDropOffSelected"
      @map-select="handleMapSelect"
    />

    <PassengerSelectionDialog
      v-model="passengerSelectionOpen"
      :route-id="currentRouteId || 4"
      :service-index="currentServiceIndex || 0"
      :initial-counts="getCurrentPassengerCounts()"
      @passenger-selected="handlePassengerSelected"
      @reserve-without-seat="handleReserveWithoutSeat"
      @proceed-to-seat="handleProceedToSeat"
    />

    <MapSelectionDialog
      v-model="mapSelectionOpen"
      title="停留所の場所"
      @stop-detail="handleMapStopSelected"
    />

    <SeatSelectionDialog
      v-model="seatSelectionOpen"
      :passenger-counts="getCurrentPassengerCounts()"
      @seats-selected="handleSeatsSelected"
    />

    <LoginDialog
      v-model="loginDialogOpen"
      @login="handleLogin"
      @register="handleRegister"
    />

    <div v-show="showBackToTop" class="back-to-top-btn" @click="scrollToTop">
      <div class="back-to-top-icon">
        <div class="icon-line"></div>
        <div class="icon-arrow">
          <div class="arrow-stem"></div>
        </div>
      </div>
    </div>

    <div class="bottom-fixed-container">
      <div class="bottom-content">
        <div class="total-summary">
          <div class="total-amount-section">
            <div class="total-fixed">
              <div class="total-title">合計金額</div>
              <div class="total-amount-display">0,000,000</div>
              <div class="total-title">円</div>
            </div>
            <div class="total-note">※人数を選択すると表示されます</div>
            <div class="total-time">合計時間: 11時間35分</div>
          </div>
        </div>
        <div class="confirm-button-section">
          <v-btn
            :class="['confirm-btn', { disabled: !isConfirmButtonEnabled }]"
            :disabled="!isConfirmButtonEnabled"
            rounded="8"
            @click="proceedToNextStep"
          >
            {{
              routeStore.currentSelectedRouteDirection === 'outbound'
                ? '予約確認'
                : routeStore.currentSelectedRouteDirection === 'inbound'
                ? '復路の選択へ'
                : '予約確認'
            }}
          </v-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.passenger-selection-page {
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

.page-header {
  background-color: #ffffff;
  border-bottom: 0.5px solid #dfdfdf;
  padding: 12px 16px;
  position: sticky;
  top: 50px;
  z-index: 99;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  max-width: 343px;
  margin: 0 auto;
}

.back-button {
  position: absolute;
  left: 0;
  width: 24px;
  height: 24px;
  min-width: 24px;
}

.header-info {
  text-align: center;
}

.page-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
  margin: 0;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.guidance-section {
  color: #26499d;
  padding: 16px 16px;
}

.bus-schedule-list {
  padding: 0 16px 16px;
  width: 100%;
  margin: 0 auto;
}

.schedule-item {
  display: flex;
  align-items: center;
  justify-content: left;
  padding: 16px 0;
  border-bottom: 1px solid #e2e8f0;
}

.schedule-item:last-child {
  border-bottom: none;
}

.schedule-number {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  color: #26499d;
}

.seats-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: 22px;
}

.seats-count {
  font-size: 18px;
  font-weight: 700;
  line-height: 1.2;
  color: #1e293b;
}

.low-seats-warning {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  color: #999;
  padding: 4px 8px;
}

.guidance-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 343px;
  margin: 0 auto;
}

.guidance-text {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5;
  color: #26499d;
  margin: 0;
  text-align: left;
}

.refresh-button {
  padding: 6px 12px !important;
  border-radius: 6px !important;
  background-color: #e7f2fa !important;
  line-height: 1.2 !important;
}

.date-selection-area {
  background-color: #e7f2fa;
  border-top: 1px solid #9cbcd4;
  padding: 12px 0 24px;
}

.date-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 20px;
}

.date-info-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-label {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: #000000;
  text-align: center;
}

.date-display-box {
  border-radius: 5px;
  padding: 4px 12px;
  display: flex;
  align-items: center;
}

.year-text {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  color: #000000;
  text-align: center;
}

.date-text {
  font-size: 22px;
  font-weight: 700;
  line-height: 1.36;
  color: #000000;
  text-align: center;
}

.date-change-button {
  height: 32px !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;
  line-height: 1.2;
  color: #26499d !important;
}

.filter-button-area {
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.filter-button {
  background-color: #ffffff;
  height: 50px !important;
  padding: 12px 16px;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;
  color: #000000 !important;
}

.bus-content-area {
  background-color: #e7f2fa;
  padding: 0 0 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.content-placeholder {
  width: 100%;
  background-color: transparent;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0 16px;
}

.bus-schedule-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.trip-card {
  background-color: #ffffff;
  border: 1px solid #95a3c4;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
}
.trip-card-content {
  padding: 0 16px 16px;
}

.trip-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  border-bottom: 1px solid #f1f5f9;
}

.trip-left-section {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trip-right-section {
  display: flex;
  align-items: center;
}

.rating-count {
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  line-height: 1.2;
  margin-right: 5px;
}

.trip-number {
  font-size: 18px;
  font-weight: 700;
  color: #26499d;
  line-height: 2;
  display: flex;
  align-items: center;
  background-color: #e7f2fa;
  padding: 5px 11px;
}

.route-info {
  font-size: 16px;
  font-weight: 500;
  color: #334155;
  line-height: 1.4;
  margin-left: 15px;
}

.trip-info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  flex-shrink: 0;
}

.info-text {
  font-size: 14px;
  font-weight: 400;
  color: #334155;
  line-height: 1.5;
}

.tags-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.tag-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-icon.daytime {
  background-color: #ffa500;
}

.tag-icon.price {
  background-color: #ffa500;
}

.tag-icon.feature {
  background-color: #26499d;
}

.tag-label {
  font-size: 12px;
  font-weight: 500;
  color: #334155;
  line-height: 1.2;
}

.selection-fields {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 12px;
}

.input-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 14px;
  font-weight: 500;
  color: #334155;
  line-height: 1.2;
  margin-bottom: 4px;
}

.seat-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.primary-action-button {
  height: 50px !important;
  background-color: #26499d !important;
  color: white !important;
}

.primary-action-button:disabled {
  background-color: #94a3b8 !important;
  color: #f8fafc !important;
  box-shadow: none !important;
}

.bus-info-section {
  margin-bottom: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.date-info {
  font-size: 14px;
  font-weight: 700;
  color: #334155;
  line-height: 1.2;
  margin-bottom: 8px;
}

.service-info {
  font-size: 16px;
  font-weight: 500;
  color: #334155;
  line-height: 1.4;
  margin-bottom: 6px;
}

.company-info {
  font-size: 14px;
  font-weight: 400;
  color: #26499d;
  line-height: 1.2;
}

.amenities {
  position: relative;
  width: 100%;
  margin-bottom: 16px;
}

.amenities-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.amenity-chip {
  border: 1px solid #9cbcd4 !important;
  border-radius: 26px !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
  background-color: #ffffff !important;
  color: #000000 !important;
}

.toggle-button {
  position: absolute;
  right: 0;
  top: 40px;
  line-height: 1.2;
  color: #26499d;
  text-align: right;
  cursor: pointer;
  user-select: none;
}

.btn-center-area {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 16px;
  margin-bottom: 20px;
}

.time-status-container {
  display: flex;
  align-items: center;
  justify-content: left;
  margin: 13px 0;
}

.time-display {
  background-color: #e7f2fa;
  border-radius: 12px;
  padding: 2px 9px;
}

.time-text {
  color: #1976d2;
  font-weight: 600;
  font-size: 14px;
}

.status-icons {
  display: flex;
  gap: 4px;
  margin-left: 10px;
}

.status-btn {
  width: 21px !important;
  height: 21px !important;
  border-radius: 50% !important;
  min-width: unset !important;
}

.status-text {
  color: white !important;
  font-weight: bold;
  font-size: 14px;
}
.passenger-count-fields {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.passenger-field {
  flex: 1;
}

.passenger-display-input {
  background-color: white;
}

.passenger-details-stacked {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.passenger-details-inline {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.passenger-field-full {
  width: 100%;
}

.passenger-details-textarea {
  width: 100%;
}

.back-to-top-btn {
  position: fixed;
  bottom: 120px;
  right: 0px;
  width: 45px;
  height: 45px;
  background-color: #26499d;
  border: 1px solid #9cbcd4;
  border-right: none;
  border-radius: 5px 0px 0px 5px;
  display: flex;
  justify-content: center;
  align-items: center;

  z-index: 1000;
  transition: all 0.3s ease;
}

.back-to-top-icon {
  position: relative;
  width: 23.33px;
  height: 26.25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.icon-line {
  width: 20px;
  height: 3px;
  background-color: #ffffff;
  border-radius: 1.5px;
  margin-bottom: 4px;
}

.icon-arrow {
  position: relative;
  width: 16px;
  height: 16px;
}

.icon-arrow::before,
.icon-arrow::after {
  content: '';
  position: absolute;
  width: 3px;
  height: 10px;
  background-color: #ffffff;
  border-radius: 1.5px;
}

.icon-arrow::before {
  transform: rotate(45deg);
  left: 3px;
  top: 2px;
}

.icon-arrow::after {
  transform: rotate(-45deg);
  right: 3px;
  top: 2px;
}

.icon-arrow .arrow-stem {
  position: absolute;
  width: 3px;
  height: 12px;
  background-color: #ffffff;
  border-radius: 1.5px;
  left: 50%;
  top: 6px;
  transform: translateX(-50%);
}

.bottom-fixed-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1px solid #dfdfdf;
  box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.bottom-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  max-width: 100%;
  margin: 0 auto;
}

.total-summary {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.total-amount-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.total-title {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.333;
  color: #000000;
}

.total-amount-display {
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
  margin: 4px 0;
  margin-left: 7px;
}

.total-note {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.2;
  color: #7d7d7d;
}

.total-time {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.375;
  color: #000000;
}

.confirm-button-section {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.confirm-btn {
  width: 120px;
  height: 48px;
  background-color: #26499d !important;
  line-height: 1.2 !important;
  color: #ffffff !important;
  text-transform: none !important;
  box-shadow: 0px 2px 4px 0px rgba(38, 73, 157, 0.3) !important;
}

.confirm-btn.disabled,
.confirm-btn:disabled {
  background-color: #c4c4c4 !important;
  color: #7d7d7d !important;
  box-shadow: none !important;
  cursor: not-allowed !important;
  opacity: 1 !important;
}

.confirm-btn:not(.disabled):not(:disabled) {
  background-color: #26499d !important;
  color: #ffffff !important;
  box-shadow: 0px 2px 4px 0px rgba(38, 73, 157, 0.3) !important;
  cursor: pointer !important;
}

.action-buttons-container {
  display: flex;
  justify-content: stretch;
  align-items: stretch;
  gap: 8px;
  padding: 0 16px;
  margin-bottom: 16px;
}

.temp-save-btn {
  height: 32px !important;
  background-color: #dfdfdf !important;
  color: #7d7d7d !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
  text-transform: none !important;
  border-radius: 4px !important;
  padding: 6px 16px !important;
}

.total-fixed {
  display: flex;
  align-items: center;
}
.tate-info-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* ルート方向表示のスタイル */
.route-direction-indicator {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-left: 4px solid #26499d;
  margin-bottom: 16px;
}

.direction-text {
  font-size: 16px;
  font-weight: 600;
  color: #26499d;
}
</style>
